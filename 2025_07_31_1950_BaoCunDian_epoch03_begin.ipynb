{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": 3, "metadata": {"id": "VCEUkYYtjg18", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8fee2cbc-acb2-4eef-b754-23af75069b8e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n", "Using device: cuda\n"]}], "source": ["import os\n", "from google.colab import drive\n", "\n", "# 挂载 Google Drive\n", "drive.mount('/content/drive')\n", "\n", "# 检查 GPU 是否可用，并设置 device\n", "import torch\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "source": ["!pip install nltk bert-score"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SJ9S0Dndjsi9", "outputId": "ca84772e-edd9-40ee-f26f-d573efdff91f"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: nltk in /usr/local/lib/python3.11/dist-packages (3.9.1)\n", "Requirement already satisfied: bert-score in /usr/local/lib/python3.11/dist-packages (0.3.13)\n", "Requirement already satisfied: click in /usr/local/lib/python3.11/dist-packages (from nltk) (8.2.1)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.11/dist-packages (from nltk) (1.5.1)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.11/dist-packages (from nltk) (2024.11.6)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from nltk) (4.67.1)\n", "Requirement already satisfied: torch>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from bert-score) (2.6.0+cu124)\n", "Requirement already satisfied: pandas>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from bert-score) (2.2.2)\n", "Requirement already satisfied: transformers>=3.0.0 in /usr/local/lib/python3.11/dist-packages (from bert-score) (4.54.0)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from bert-score) (2.0.2)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from bert-score) (2.32.3)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (from bert-score) (3.10.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.11/dist-packages (from bert-score) (25.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.0.1->bert-score) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.0.1->bert-score) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.0.1->bert-score) (2025.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (4.14.1)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (2025.3.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.3.1.170)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.0.0->bert-score) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.0.0->bert-score) (1.3.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.34.0 in /usr/local/lib/python3.11/dist-packages (from transformers>=3.0.0->bert-score) (0.34.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers>=3.0.0->bert-score) (6.0.2)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers>=3.0.0->bert-score) (0.21.2)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers>=3.0.0->bert-score) (0.5.3)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->bert-score) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib->bert-score) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->bert-score) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->bert-score) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib->bert-score) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->bert-score) (3.2.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->bert-score) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->bert-score) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->bert-score) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->bert-score) (2025.7.14)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.34.0->transformers>=3.0.0->bert-score) (1.1.5)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas>=1.0.1->bert-score) (1.17.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.0.0->bert-score) (3.0.2)\n"]}]}, {"cell_type": "code", "source": ["import unicodedata\n", "import re\n", "import random\n", "import time\n", "import math\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch import optim\n", "import torch.nn.functional as F\n", "\n", "import nltk\n", "from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction\n", "from nltk.translate.meteor_score import meteor_score\n", "from bert_score import score as bert_scorer\n", "nltk.download('punkt')\n", "nltk.download('wordnet')\n", "nltk.download('omw-1.4')\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h_RIDOzpj1Ue", "outputId": "5bfcea7b-08c4-462b-920a-884a5f7c8c8a"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package wordnet to /root/nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n", "[nltk_data] Downloading package omw-1.4 to /root/nltk_data...\n", "[nltk_data]   Package omw-1.4 is already up-to-date!\n"]}]}, {"cell_type": "code", "source": ["# 定义特殊 token\n", "SOS_token = 0  # Start Of Sentence\n", "EOS_token = 1  # End Of Sentence\n", "PAD_token = 2  # Padding\n", "\n", "# --- 1. 解压数据集 ---\n", "# 添加 -o 参数以自动覆盖，避免交互式提示\n", "!unzip -oq /content/drive/MyDrive/Cooking_Dataset.zip -d /content/\n", "\n", "# --- 路径设置 ---\n", "data_path = '/content/Cooking_Dataset/'\n", "train_path = os.path.join(data_path, 'train.csv')\n", "dev_path = os.path.join(data_path, 'dev.csv')\n", "test_path = os.path.join(data_path, 'test.csv')\n", "\n", "\n", "# --- 2. 词汇表类 (Lang) ---\n", "class Lang:\n", "    def __init__(self, name):\n", "        self.name = name\n", "        self.word2index = {}\n", "        self.word2count = {}\n", "        self.index2word = {SOS_token: \"SOS\", EOS_token: \"EOS\", PAD_token: \"PAD\"}\n", "        self.n_words = 3  # 计算 SOS, EOS, PAD\n", "\n", "    def addSentence(self, sentence):\n", "        for word in sentence.split(' '):\n", "            self.addWord(word)\n", "\n", "    def addWord(self, word):\n", "        if word not in self.word2index:\n", "            self.word2index[word] = self.n_words\n", "            self.word2count[word] = 1\n", "            self.index2word[self.n_words] = word\n", "            self.n_words += 1\n", "        else:\n", "            self.word2count[word] += 1\n", "\n", "# --- 3. 文本预处理函数 ---\n", "def unicodeTo<PERSON>(s):\n", "    return ''.join(\n", "        c for c in unicodedata.normalize('NFD', s)\n", "        if unicodedata.category(c) != 'Mn'\n", "    )\n", "\n", "def normalizeString(s):\n", "    s = unicodeToAscii(s.lower().strip())\n", "    s = re.sub(r\"([.!?,'\\[\\]\\\"()])\", r\" \\1 \", s)\n", "    s = re.sub(r'\\s+', ' ', s).strip()\n", "    return s\n", "\n", "# --- 4. 读取和处理数据 (已修正) ---\n", "def read_data(path):\n", "    df = pd.read_csv(path)\n", "\n", "    # 将列名重命名为小写\n", "    df.rename(columns={\n", "        'Title': 'title',\n", "        'Ingredients': 'ingredients',\n", "        'Recipe': 'recipe'\n", "    }, inplace=True, errors='ignore')\n", "\n", "    # 格式化 ingredients: \"['ing1', 'ing2']\" -> \"ing1 , ing2\"\n", "    df['ingredients'] = df['ingredients'].apply(lambda x: ', '.join(eval(x)))\n", "\n", "    # **新修正部分：**\n", "    # 格式化 recipe: \"['step 1', 'step 2']\" -> \"step 1 step 2\"\n", "    # 这样可以移除列表格式的标点符号，并将所有步骤合并成一个长字符串\n", "    df['recipe'] = df['recipe'].apply(lambda x: ' '.join(eval(x)))\n", "\n", "    pairs = df[['ingredients', 'recipe']].values.tolist()\n", "    return pairs\n", "\n", "def prepareData(lang1_name, lang2_name, path):\n", "    print(\"Reading lines...\")\n", "    pairs = read_data(path)\n", "    print(f\"Read {len(pairs)} sentence pairs\")\n", "\n", "    pairs = [[normalizeString(s) for s in pair] for pair in pairs]\n", "\n", "    input_lang = Lang(lang1_name)\n", "    output_lang = Lang(lang2_name)\n", "\n", "    return input_lang, output_lang, pairs\n", "\n", "# --- 5. 主数据准备流程 ---\n", "input_lang, output_lang, train_pairs = prepareData('ingredients', 'recipe', train_path)\n", "dev_pairs = [[normalizeString(s) for s in pair] for pair in read_data(dev_path)]\n", "test_pairs = [[normalizeString(s) for s in pair] for pair in read_data(test_path)]\n", "\n", "print(\"Building vocabulary...\")\n", "for pair in train_pairs:\n", "    input_lang.addSentence(pair[0])\n", "    output_lang.addSentence(pair[1])\n", "print(\"Vocabulary built!\")\n", "print(f\"Input language ({input_lang.name}) words: {input_lang.n_words}\")\n", "print(f\"Output language ({output_lang.name}) words: {output_lang.n_words}\")\n", "print(\"\\nSample data pair:\")\n", "print(random.choice(train_pairs))\n", "\n", "# --- 6. 将文本转换为 Tensor ---\n", "def indexesFromSentence(lang, sentence):\n", "    # 增加一个检查，如果单词不在词汇表中（可能出现在 dev/test 集），则跳过\n", "    return [lang.word2index[word] for word in sentence.split(' ') if word in lang.word2index]\n", "\n", "def tensorFromSentence(lang, sentence):\n", "    indexes = indexesFromSentence(lang, sentence)\n", "    indexes.append(EOS_token)\n", "    return torch.tensor(indexes, dtype=torch.long, device=device).view(-1, 1)\n", "\n", "def tensorsFromPair(pair):\n", "    input_tensor = tensorFromSentence(input_lang, pair[0])\n", "    target_tensor = tensorFromSentence(output_lang, pair[1])\n", "    return (input_tensor, target_tensor)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cqiLL_rYj1XA", "outputId": "39c41e1b-87cc-47bc-c178-1664370c1ea4"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reading lines...\n", "Read 162899 sentence pairs\n", "Building vocabulary...\n", "Vocabulary built!\n", "Input language (ingredients) words: 12082\n", "Output language (recipe) words: 18878\n", "\n", "Sample data pair:\n", "['1 large can pineapple chunks , 4 c . miniature marshmallows , 1 c . crushed peanuts , 3 heaping tbsp . sugar , 3 heaping tbsp . flour , 1 egg', 'drain pineapple; reserve liquid . mix sugar , flour and egg together; gradually stir in pineapple liquid . cook over medium heat until thickened . let cool . add marshmallows and pineapple; mix well . put in serving dish and top with crushed peanuts . refrigerate .']\n"]}]}, {"cell_type": "code", "source": ["class RecipeDataset(Dataset):\n", "    def __init__(self, pairs):\n", "        self.pairs = pairs\n", "\n", "    def __len__(self):\n", "        return len(self.pairs)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.pairs[idx]\n", "\n", "def collate_fn(batch):\n", "    # 按输入序列长度降序排序（对 pack_padded_sequence 很重要）\n", "    batch.sort(key=lambda x: len(x[0].split(' ')), reverse=True)\n", "\n", "    input_sentences, target_sentences = zip(*batch)\n", "\n", "    input_tensors = [tensorFromSentence(input_lang, s).squeeze(1) for s in input_sentences]\n", "    target_tensors = [tensorFromSentence(output_lang, s).squeeze(1) for s in target_sentences]\n", "\n", "    # 计算每个序列的原始长度\n", "    input_lengths = [len(t) for t in input_tensors]\n", "    target_lengths = [len(t) for t in target_tensors]\n", "\n", "    # 填充序列\n", "    input_padded = nn.utils.rnn.pad_sequence(input_tensors, batch_first=True, padding_value=PAD_token)\n", "    target_padded = nn.utils.rnn.pad_sequence(target_tensors, batch_first=True, padding_value=PAD_token)\n", "\n", "    return input_padded, torch.tensor(input_lengths), target_padded, torch.tensor(target_lengths)\n", "\n", "\n", "BATCH_SIZE = 32\n", "\n", "train_dataset = RecipeDataset(train_pairs)\n", "dev_dataset = RecipeDataset(dev_pairs)\n", "test_dataset = RecipeDataset(test_pairs)\n", "\n", "train_dataloader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, collate_fn=collate_fn)\n", "dev_dataloader = DataLoader(dev_dataset, batch_size=BATCH_SIZE, shuffle=False, collate_fn=collate_fn)\n", "test_dataloader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, collate_fn=collate_fn)\n", "\n", "# 打印一个批次的数据形状以验证\n", "for batch in train_dataloader:\n", "    input_padded, input_lengths, target_padded, target_lengths = batch\n", "    print(\"Input padded shape:\", input_padded.shape)\n", "    print(\"Input lengths:\", input_lengths.shape)\n", "    print(\"Target padded shape:\", target_padded.shape)\n", "    print(\"Target lengths:\", target_lengths.shape)\n", "    break"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pcQETfVSj1aU", "outputId": "db0e44e5-ee2a-4fe5-e51a-864c505f0368"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Input padded shape: torch.Size([32, 105])\n", "Input lengths: <PERSON>.<PERSON><PERSON>([32])\n", "Target padded shape: torch.Size([32, 99])\n", "Target lengths: <PERSON><PERSON><PERSON><PERSON>([32])\n"]}]}, {"cell_type": "code", "source": ["class EncoderRNN(nn.Module):\n", "    def __init__(self, input_size, hidden_size, num_layers=1):\n", "        super(Encode<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "\n", "        self.embedding = nn.Embedding(input_size, hidden_size)\n", "        self.gru = nn.GRU(hidden_size, hidden_size, num_layers=num_layers, batch_first=True)\n", "\n", "    def forward(self, input, input_lengths, hidden):\n", "        embedded = self.embedding(input)\n", "        packed = nn.utils.rnn.pack_padded_sequence(embedded, input_lengths.cpu(), batch_first=True)\n", "        outputs, hidden = self.gru(packed, hidden)\n", "        outputs, _ = nn.utils.rnn.pad_packed_sequence(outputs, batch_first=True)\n", "        return outputs, hidden\n", "\n", "    def initHidden(self, batch_size):\n", "        return torch.zeros(self.num_layers, batch_size, self.hidden_size, device=device)\n", "\n", "class DecoderRNN(nn.Module):\n", "    def __init__(self, hidden_size, output_size, num_layers=1):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "\n", "        self.embedding = nn.Embedding(output_size, hidden_size)\n", "        self.gru = nn.GRU(hidden_size, hidden_size, num_layers=num_layers, batch_first=True)\n", "        self.out = nn.Linear(hidden_size, output_size)\n", "        self.softmax = nn.LogSoftmax(dim=1)\n", "\n", "    def forward(self, input, hidden):\n", "        output = self.embedding(input).view(input.size(0), 1, -1)\n", "        output = F.relu(output)\n", "        output, hidden = self.gru(output, hidden)\n", "        output = self.softmax(self.out(output.squeeze(1)))\n", "        return output, hidden\n", "\n", "class Seq2Seq(nn.Module):\n", "    def __init__(self, encoder, decoder, device):\n", "        super(Seq2Seq, self).__init__()\n", "        self.encoder = encoder\n", "        self.decoder = decoder\n", "        self.device = device\n", "\n", "    def forward(self, src, src_len, trg, teacher_forcing_ratio=0.5):\n", "        batch_size = trg.shape[0]\n", "        trg_len = trg.shape[1]\n", "        trg_vocab_size = self.decoder.out.out_features\n", "\n", "        # 存储解码器输出的张量\n", "        outputs = torch.zeros(batch_size, trg_len, trg_vocab_size).to(self.device)\n", "\n", "        # 编码器的最后一个隐藏状态作为解码器的初始隐藏状态\n", "        encoder_hidden = self.encoder.initHidden(batch_size)\n", "        _, encoder_hidden = self.encoder(src, src_len, encoder_hidden)\n", "\n", "        # 第一个输入是 SOS token\n", "        decoder_input = torch.full((batch_size, 1), SOS_token, device=self.device, dtype=torch.long)\n", "\n", "        decoder_hidden = encoder_hidden\n", "\n", "        use_teacher_forcing = True if random.random() < teacher_forcing_ratio else False\n", "\n", "        if use_teacher_forcing:\n", "            # 使用 Teacher forcing：将真实目标作为下一个输入\n", "            for t in range(trg_len -1):\n", "                decoder_output, decoder_hidden = self.decoder(decoder_input.squeeze(1), decoder_hidden)\n", "                outputs[:, t, :] = decoder_output\n", "                decoder_input = trg[:, t+1].unsqueeze(1) # Teacher forcing\n", "        else:\n", "            # 不使用 Teacher forcing：使用自己的预测作为下一个输入\n", "            for t in range(trg_len - 1):\n", "                decoder_output, decoder_hidden = self.decoder(decoder_input.squeeze(1), decoder_hidden)\n", "                outputs[:, t, :] = decoder_output\n", "                topv, topi = decoder_output.topk(1)\n", "                decoder_input = topi.detach() # 从历史中分离\n", "\n", "        return outputs"], "metadata": {"id": "ZlOR5usmj1b8"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["# --- 修正后的第 7 单元格：可恢复的训练代码 ---\n", "\n", "# (asMinutes 和 timeSince 函数保持不变)\n", "def asMinutes(s):\n", "    m = math.floor(s / 60)\n", "    s -= m * 60\n", "    return '%dm %ds' % (m, s)\n", "\n", "def timeSince(since, percent):\n", "    now = time.time()\n", "    s = now - since\n", "    es = s / (percent)\n", "    rs = es - s\n", "    return '%s (- %s)' % (asMinutes(s), asMinutes(rs))\n", "\n", "def train(model, dataloader, optimizer, criterion):\n", "    model.train()\n", "    epoch_loss = 0\n", "\n", "    for i, batch in enumerate(dataloader):\n", "        src, src_len, trg, trg_len = batch\n", "        src, trg = src.to(device), trg.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(src, src_len, trg)\n", "        output_dim = output.shape[-1]\n", "        output = output[:, :-1, :].reshape(-1, output_dim)\n", "        trg = trg[:, 1:].reshape(-1)\n", "\n", "        loss = criterion(output, trg)\n", "        loss.backward()\n", "        torch.nn.utils.clip_grad_norm_(model.parameters(), 1)\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "    return epoch_loss / len(dataloader)\n", "\n", "def showPlot(points):\n", "    plt.figure()\n", "    fig, ax = plt.subplots()\n", "    loc = ticker.MultipleLocator(base=0.2)\n", "    ax.yaxis.set_major_locator(loc)\n", "    plt.plot(points)\n", "    plt.show()\n", "\n", "# --- 新的、可恢复的训练流程 ---\n", "\n", "# 1. 初始化模型和优化器 (和之前一样)\n", "HIDDEN_SIZE = 256\n", "NUM_LAYERS = 1\n", "N_EPOCHS = 10 # 目标总 epoch 数\n", "LEARNING_RATE = 0.01\n", "\n", "encoder1 = EncoderRNN(input_lang.n_words, HIDDEN_SIZE, NUM_LAYERS).to(device)\n", "decoder1 = DecoderRNN(HIDDEN_SIZE, output_lang.n_words, NUM_LAYERS).to(device)\n", "model_baseline1 = Seq2Seq(encoder1, decoder1, device).to(device)\n", "\n", "optimizer = optim.SGD(model_baseline1.parameters(), lr=LEARNING_RATE)\n", "criterion = nn.NLLLoss(ignore_index=PAD_token)\n", "\n", "# 2. 定义检查点路径并尝试加载\n", "CHECKPOINT_PATH = \"/content/drive/MyDrive/checkpoint_baseline1_epoch2.pth\"\n", "start_epoch = 1\n", "plot_losses = []\n", "\n", "# 检查文件是否存在\n", "if os.path.exists(CHECKPOINT_PATH):\n", "    print(\"发现检查点文件，正在加载...\")\n", "    checkpoint = torch.load(CHECKPOINT_PATH)\n", "\n", "    model_baseline1.load_state_dict(checkpoint['model_state_dict'])\n", "    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "    start_epoch = checkpoint['epoch'] + 1 # 从下一个 epoch 开始\n", "\n", "    print(f\"加载成功！将从 epoch {start_epoch} 继续训练。\")\n", "else:\n", "    print(\"未发现检查点，将从头开始训练。\")\n", "\n", "\n", "# 3. 修改后的训练循环\n", "print(f\"开始训练，从 epoch {start_epoch} 到 {N_EPOCHS}...\")\n", "start_time = time.time()\n", "\n", "for epoch in range(start_epoch, N_EPOCHS + 1):\n", "    loss = train(model_baseline1, train_dataloader, optimizer, criterion)\n", "    plot_losses.append(loss)\n", "\n", "    # 打印日志\n", "    print('%s (%d %d%%) %.4f' % (timeSince(start_time, (epoch - start_epoch + 1) / (N_EPOCHS - start_epoch + 1)),\n", "                                     epoch, epoch / N_EPOCHS * 100, loss))\n", "\n", "print(\"训练完成！\")\n", "showPlot(plot_losses)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 388}, "id": "7R3R9kjXj1eA", "outputId": "b3f99f40-e123-4995-ad5e-afa1b73415ba"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Training for 10 epochs...\n", "62m 30s (- 562m 37s) (1 10%) 6.2353\n", "124m 45s (- 499m 1s) (2 20%) 5.6213\n"]}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipython-input-390608941.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     92\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     93\u001b[0m \u001b[0;31m# 开始训练\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 94\u001b[0;31m \u001b[0mmodel_baseline1\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtrainIters\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel_baseline1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mN_EPOCHS\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtrain_dataloader\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mprint_every\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/tmp/ipython-input-390608941.py\u001b[0m in \u001b[0;36mtrainIters\u001b[0;34m(model, n_epochs, dataloader, print_every, plot_every, learning_rate)\u001b[0m\n\u001b[1;32m     55\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"Training for {n_epochs} epochs...\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     56\u001b[0m     \u001b[0;32mfor\u001b[0m \u001b[0mepoch\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mn_epochs\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 57\u001b[0;31m         \u001b[0mloss\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtrain\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdataloader\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0moptimizer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcriterion\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     58\u001b[0m         \u001b[0mprint_loss_total\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0mloss\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     59\u001b[0m         \u001b[0mplot_loss_total\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0mloss\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/tmp/ipython-input-390608941.py\u001b[0m in \u001b[0;36mtrain\u001b[0;34m(model, dataloader, optimizer, criterion)\u001b[0m\n\u001b[1;32m     39\u001b[0m         \u001b[0moptimizer\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstep\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     40\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 41\u001b[0;31m         \u001b[0mepoch_loss\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0mloss\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     42\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     43\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0mepoch_loss\u001b[0m \u001b[0;34m/\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdataloader\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}]}, {"cell_type": "code", "source": ["# --- 保存模型的代码单元格 ---\n", "\n", "# 1. 定义保存路径（保存在 Google Drive 中，这样不会丢失）\n", "# 因为你完成了 2 个 epoch，我们就这样命名\n", "SAVE_PATH = \"/content/drive/MyDrive/checkpoint_baseline1_epoch2.pth\"\n", "\n", "# 2. 准备要保存的内容\n", "# 我们需要保存模型权重、优化器状态和当前的 epoch 数\n", "# 注意：你需要确保 model_baseline1 和 optimizer 这两个变量在环境中是存在的\n", "# 因为我们刚刚停止了训练，所以它们肯定是存在的。\n", "# 我们需要重新创建一个 optimizer 实例，因为之前的 optimizer 在 trainIters 函数内部。\n", "learning_rate = 0.01 # 确保这个学习率和你训练时用的一致\n", "optimizer = optim.SGD(model_baseline1.parameters(), lr=learning_rate)\n", "\n", "# 3. 执行保存\n", "torch.save({\n", "    'epoch': 2, # 您完成了 2 个 epoch\n", "    'model_state_dict': model_baseline1.state_dict(),\n", "    'optimizer_state_dict': optimizer.state_dict(),\n", "    # 如果您想保存 loss，也可以加进去\n", "}, SAVE_PATH)\n", "\n", "print(f\"模型已成功保存到: {SAVE_PATH}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0W2rvZcOTy64", "outputId": "1cd7449a-fae3-466d-bc46-2014541616c0"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["模型已成功保存到: /content/drive/MyDrive/checkpoint_baseline1_epoch2.pth\n"]}]}, {"cell_type": "code", "source": ["def evaluate(model, dataloader):\n", "    model.eval()\n", "    predictions = []\n", "    actuals = []\n", "\n", "    with torch.no_grad():\n", "        for i, batch in enumerate(dataloader):\n", "            src, src_len, trg, _ = batch\n", "            src, trg = src.to(device), trg.to(device)\n", "\n", "            # 生成预测\n", "            output = model(src, src_len, trg, teacher_forcing_ratio=0) # 在评估时关闭 teacher forcing\n", "\n", "            # output: [batch_size, trg_len, vocab_size]\n", "            # 取概率最高的 token 作为预测结果\n", "            predicted_indices = output.argmax(2) # [batch_size, trg_len]\n", "\n", "            # 将索引转换回单词\n", "            for j in range(predicted_indices.shape[0]):\n", "                pred_list = []\n", "                for token_idx in predicted_indices[j]:\n", "                    token_idx = token_idx.item()\n", "                    if token_idx == EOS_token:\n", "                        break\n", "                    if token_idx != PAD_token and token_idx != SOS_token:\n", "                         pred_list.append(output_lang.index2word[token_idx])\n", "                predictions.append(' '.join(pred_list))\n", "\n", "            # 准备真实食谱\n", "            for j in range(trg.shape[0]):\n", "                trg_list = []\n", "                # 从 1 开始以跳过 SOS token\n", "                for token_idx in trg[j, 1:]:\n", "                    token_idx = token_idx.item()\n", "                    if token_idx == EOS_token:\n", "                        break\n", "                    if token_idx != PAD_token:\n", "                        trg_list.append(output_lang.index2word[token_idx])\n", "                actuals.append(' '.join(trg_list))\n", "\n", "    return predictions, actuals\n", "\n", "def calculate_scores(predictions, actuals):\n", "    bleu4_scores = []\n", "    meteor_scores = []\n", "\n", "    # 将真实文本和预测文本分词，用于 NLTK\n", "    tokenized_actuals_for_nltk = [[sent.split()] for sent in actuals]\n", "    tokenized_preds_for_nltk = [sent.split() for sent in predictions]\n", "\n", "    # 计算 BLEU 和 METEOR\n", "    smoothie = SmoothingFunction().method4\n", "    for i in range(len(tokenized_preds_for_nltk)):\n", "        # BLEU\n", "        bleu_score = sentence_bleu(tokenized_actuals_for_nltk[i], tokenized_preds_for_nltk[i], weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothie)\n", "        bleu4_scores.append(bleu_score)\n", "\n", "        # METEOR (NLTK 的 meteor_score 需要分词后的列表)\n", "        # Note: meteor_score takes a list of references, but we have one.\n", "        m_score = meteor_score(tokenized_actuals_for_nltk[i], tokenized_preds_for_nltk[i])\n", "        meteor_scores.append(m_score)\n", "\n", "    # 计算 BERTScore\n", "    # bert-score 需要字符串列表\n", "    P, R, <PERSON> = bert_scorer(predictions, actuals, lang='en', verbose=False)\n", "\n", "    avg_bleu = np.mean(bleu4_scores)\n", "    avg_meteor = np.mean(meteor_scores)\n", "    avg_bert_f1 = F1.mean().item()\n", "\n", "    print(f\"Average BLEU-4 Score: {avg_bleu:.4f}\")\n", "    print(f\"Average METEOR Score: {avg_meteor:.4f}\")\n", "    print(f\"Average BERTScore (F1): {avg_bert_f1:.4f}\")\n", "\n", "    return avg_bleu, avg_meteor, avg_bert_f1\n", "\n", "# --- 在测试集上运行评估 ---\n", "print(\"\\nEvaluating on Test Set...\")\n", "predictions, actuals = evaluate(model_baseline1, test_dataloader)\n", "calculate_scores(predictions, actuals)\n", "\n", "\n", "# --- 打印一些样本 ---\n", "print(\"\\n--- Sample Predictions ---\")\n", "for i in range(5):\n", "    idx = random.randint(0, len(predictions)-1)\n", "    print(f\"ACTUAL: {actuals[idx]}\")\n", "    print(f\"PREDICTED: {predictions[idx]}\\n\")\n"], "metadata": {"id": "t7cX7x1Mj1gA"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "MhwT5o9gj1iH"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "UHjZiJ6fj1kC"}, "execution_count": null, "outputs": []}]}