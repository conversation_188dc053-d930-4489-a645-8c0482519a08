# === 第11个Cell: Baseline 2 评估 ===

# 重新定义和加载Baseline 2模型（如果变量未定义）
if 'model_baseline2' not in globals():
    HIDDEN_SIZE_B2 = 256
    NUM_LAYERS_B2 = 1
    
    encoder2 = AttentionEncoderRNN(input_lang.n_words, HIDDEN_SIZE_B2, NUM_LAYERS_B2).to(device)
    decoder2 = AttentionDecoderRNN(HIDDEN_SIZE_B2, output_lang.n_words, NUM_LAYERS_B2).to(device)
    model_baseline2 = Seq2SeqWithAttention(encoder2, decoder2, device).to(device)
    
    # 加载最新的检查点
    checkpoint = torch.load('./checkpoint_baseline2_epoch15.pth', map_location=device)
    model_baseline2.load_state_dict(checkpoint['model_state_dict'])
    print("Baseline 2模型已重新加载！")

def evaluate_attention_model(model, dataloader):
    """专门用于评估带注意力机制模型的函数"""
    model.eval()
    predictions = []
    actuals = []

    with torch.no_grad():
        for i, batch in enumerate(dataloader):
            src, src_len, trg, _ = batch
            src, trg = src.to(device), trg.to(device)

            # 生成预测
            output = model(src, src_len, trg, teacher_forcing_ratio=0)  # 在评估时关闭 teacher forcing

            # output: [batch_size, trg_len, vocab_size]
            # 取概率最高的 token 作为预测结果
            predicted_indices = output.argmax(2)  # [batch_size, trg_len]

            # 将索引转换回单词
            for j in range(predicted_indices.shape[0]):
                pred_list = []
                for token_idx in predicted_indices[j]:
                    token_idx = token_idx.item()
                    if token_idx == EOS_token:
                        break
                    if token_idx != PAD_token and token_idx != SOS_token:
                         pred_list.append(output_lang.index2word[token_idx])
                predictions.append(' '.join(pred_list))

            # 准备真实食谱
            for j in range(trg.shape[0]):
                trg_list = []
                # 从 1 开始以跳过 SOS token
                for token_idx in trg[j, 1:]:
                    token_idx = token_idx.item()
                    if token_idx == EOS_token:
                        break
                    if token_idx != PAD_token:
                        trg_list.append(output_lang.index2word[token_idx])
                actuals.append(' '.join(trg_list))

    return predictions, actuals

def calculate_scores_baseline2(predictions, actuals, model_name="Baseline 2"):
    """计算评估指标（专门为Baseline 2）"""
    bleu4_scores = []
    meteor_scores = []

    # 将真实文本和预测文本分词，用于 NLTK
    tokenized_actuals_for_nltk = [[sent.split()] for sent in actuals]
    tokenized_preds_for_nltk = [sent.split() for sent in predictions]

    # 计算 BLEU 和 METEOR
    smoothie = SmoothingFunction().method4
    for i in range(len(tokenized_preds_for_nltk)):
        # BLEU
        bleu_score = sentence_bleu(tokenized_actuals_for_nltk[i], tokenized_preds_for_nltk[i], 
                                 weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothie)
        bleu4_scores.append(bleu_score)

        # METEOR
        m_score = meteor_score(tokenized_actuals_for_nltk[i], tokenized_preds_for_nltk[i])
        meteor_scores.append(m_score)

    # 计算 BERTScore (暂时跳过由于版本兼容性问题)
    try:
        P, R, F1 = bert_scorer(predictions, actuals, lang='en', verbose=False)
        avg_bert_f1 = F1.mean().item()
    except Exception as e:
        print(f"BERTScore计算失败，跳过: {str(e)[:100]}...")
        avg_bert_f1 = 0.0
    
    avg_bleu = np.mean(bleu4_scores)
    avg_meteor = np.mean(meteor_scores)

    print(f"\n=== {model_name} 评估结果 ===")
    print(f"Average BLEU-4 Score: {avg_bleu:.4f}")
    print(f"Average METEOR Score: {avg_meteor:.4f}")
    print(f"Average BERTScore (F1): {avg_bert_f1:.4f}")

    return avg_bleu, avg_meteor, avg_bert_f1

# --- 在测试集上运行Baseline 2评估 ---
print("\nEvaluating Baseline 2 on Test Set...")
predictions_b2, actuals_b2 = evaluate_attention_model(model_baseline2, test_dataloader)
bleu_b2, meteor_b2, bert_b2 = calculate_scores_baseline2(predictions_b2, actuals_b2, "Baseline 2 (with Attention)")

# --- 打印一些样本 ---
print("\n--- Baseline 2 Sample Predictions ---")
for i in range(5):
    idx = random.randint(0, len(predictions_b2)-1)
    print(f"ACTUAL: {actuals_b2[idx]}")
    print(f"PREDICTED: {predictions_b2[idx]}\n")

# --- 比较Baseline 1和Baseline 2 ---
print("\n=== 模型比较 ===")
print("模型\t\t\tBLEU-4\t\tMETEOR\t\tBERTScore")
print("-" * 60)
print(f"Baseline 1 (无注意力)\t{0.0052:.4f}\t\t{0.0668:.4f}\t\t{0.0000:.4f}")
print(f"Baseline 2 (有注意力)\t{bleu_b2:.4f}\t\t{meteor_b2:.4f}\t\t{bert_b2:.4f}")

improvement_bleu = ((bleu_b2 - 0.0052) / 0.0052) * 100 if 0.0052 > 0 else 0
improvement_meteor = ((meteor_b2 - 0.0668) / 0.0668) * 100 if 0.0668 > 0 else 0

print(f"\n改进幅度:")
print(f"BLEU-4: {improvement_bleu:.1f}%")
print(f"METEOR: {improvement_meteor:.1f}%")
