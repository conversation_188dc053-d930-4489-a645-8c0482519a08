{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VCEUkYYtjg18", "outputId": "8fee2cbc-acb2-4eef-b754-23af75069b8e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n"]}], "source": ["import os\n", "import torch\n", "\n", "# 检查 GPU 是否可用，并设置 device\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SJ9S0Dndjsi9", "outputId": "ca84772e-edd9-40ee-f26f-d573efdff91f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://mirrors.tencent.com/pypi/simple/\n", "Requirement already satisfied: nltk in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (3.9.1)\n", "Requirement already satisfied: bert-score in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (0.3.13)\n", "Requirement already satisfied: click in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from nltk) (8.1.8)\n", "Requirement already satisfied: joblib in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from nltk) (1.5.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from nltk) (2024.11.6)\n", "Requirement already satisfied: tqdm in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from nltk) (4.67.1)\n", "Requirement already satisfied: torch>=1.0.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (2.0.0)\n", "Requirement already satisfied: pandas>=1.0.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (2.2.3)\n", "Requirement already satisfied: transformers>=3.0.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (4.51.3)\n", "Requirement already satisfied: numpy in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (1.24.2)\n", "Requirement already satisfied: requests in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (2.32.3)\n", "Requirement already satisfied: matplotlib in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (3.8.4)\n", "Requirement already satisfied: packaging>=20.9 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from bert-score) (23.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from pandas>=1.0.1->bert-score) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from pandas>=1.0.1->bert-score) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from pandas>=1.0.1->bert-score) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas>=1.0.1->bert-score) (1.17.0)\n", "Requirement already satisfied: filelock in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (3.18.0)\n", "Requirement already satisfied: typing-extensions in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (4.13.2)\n", "Requirement already satisfied: sympy in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (1.14.0)\n", "Requirement already satisfied: networkx in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (3.4.2)\n", "Requirement already satisfied: jinja2 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (3.1.6)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu11==11.7.99 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.7.99)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu11==11.7.99 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.7.99)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu11==11.7.101 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.7.101)\n", "Requirement already satisfied: nvidia-cudnn-cu11==8.5.0.96 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (8.5.0.96)\n", "Requirement already satisfied: nvidia-cublas-cu11==11.10.3.66 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.10.3.66)\n", "Requirement already satisfied: nvidia-cufft-cu11==10.9.0.58 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (10.9.0.58)\n", "Requirement already satisfied: nvidia-curand-cu11==10.2.10.91 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (10.2.10.91)\n", "Requirement already satisfied: nvidia-cusolver-cu11==11.4.0.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.4.0.1)\n", "Requirement already satisfied: nvidia-cusparse-cu11==11.7.4.91 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.7.4.91)\n", "Requirement already satisfied: nvidia-nccl-cu11==2.14.3 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (2.14.3)\n", "Requirement already satisfied: nvidia-nvtx-cu11==11.7.91 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (11.7.91)\n", "Requirement already satisfied: triton==2.0.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from torch>=1.0.0->bert-score) (2.0.0)\n", "Requirement already satisfied: setuptools in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from nvidia-cublas-cu11==11.10.3.66->torch>=1.0.0->bert-score) (80.4.0)\n", "Requirement already satisfied: wheel in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from nvidia-cublas-cu11==11.10.3.66->torch>=1.0.0->bert-score) (0.45.1)\n", "Requirement already satisfied: cmake in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from triton==2.0.0->torch>=1.0.0->bert-score) (4.0.2)\n", "Requirement already satisfied: lit in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from triton==2.0.0->torch>=1.0.0->bert-score) (18.1.8)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from transformers>=3.0.0->bert-score) (0.31.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from transformers>=3.0.0->bert-score) (6.0.2)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from transformers>=3.0.0->bert-score) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from transformers>=3.0.0->bert-score) (0.5.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.30.0->transformers>=3.0.0->bert-score) (2025.3.2)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.30.0->transformers>=3.0.0->bert-score) (1.1.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from jinja2->torch>=1.0.0->bert-score) (3.0.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from matplotlib->bert-score) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from matplotlib->bert-score) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from matplotlib->bert-score) (4.58.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from matplotlib->bert-score) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from matplotlib->bert-score) (10.4.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from matplotlib->bert-score) (3.2.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from requests->bert-score) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from requests->bert-score) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from requests->bert-score) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from requests->bert-score) (2025.4.26)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /root/.pyenv/versions/3.11.1/lib/python3.11/site-packages (from sympy->torch>=1.0.0->bert-score) (1.3.0)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.1.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install nltk bert-score"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h_RIDOzpj1Ue", "outputId": "5bfcea7b-08c4-462b-920a-884a5f7c8c8a"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/.pyenv/versions/3.11.1/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package wordnet to /root/nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n", "[nltk_data] Downloading package omw-1.4 to /root/nltk_data...\n", "[nltk_data]   Package omw-1.4 is already up-to-date!\n"]}], "source": ["import unicodedata\n", "import re\n", "import random\n", "import time\n", "import math\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch import optim\n", "import torch.nn.functional as F\n", "\n", "import nltk\n", "from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction\n", "from nltk.translate.meteor_score import meteor_score\n", "from bert_score import score as bert_scorer\n", "nltk.download('punkt')\n", "nltk.download('wordnet')\n", "nltk.download('omw-1.4')\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cqiLL_rYj1XA", "outputId": "39c41e1b-87cc-47bc-c178-1664370c1ea4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading lines...\n", "Read 162899 sentence pairs\n", "Building vocabulary...\n", "Vocabulary built!\n", "Input language (ingredients) words: 12082\n", "Output language (recipe) words: 18878\n", "\n", "Sample data pair:\n", "['1 c . plus 2 tbsp . milk , 1/2 c . warm water , 3/4 c . sugar', 'mix dry milk and water; set in a pan of very hot hot . add sugar , stirring until sugar is dissolved . equal to 1 can of eagle brand milk .']\n"]}], "source": ["# 定义特殊 token\n", "SOS_token = 0  # Start Of Sentence\n", "EOS_token = 1  # End Of Sentence\n", "PAD_token = 2  # Padding\n", "\n", "# --- 1. 解压数据集 ---\n", "# 添加 -o 参数以自动覆盖，避免交互式提示\n", "!unzip -oq ./Cooking_Dataset.zip -d .\n", "\n", "# --- 路径设置 ---\n", "data_path = './Cooking_Dataset/'\n", "train_path = os.path.join(data_path, 'train.csv')\n", "dev_path = os.path.join(data_path, 'dev.csv')\n", "test_path = os.path.join(data_path, 'test.csv')\n", "\n", "\n", "# --- 2. 词汇表类 (Lang) ---\n", "class Lang:\n", "    def __init__(self, name):\n", "        self.name = name\n", "        self.word2index = {}\n", "        self.word2count = {}\n", "        self.index2word = {SOS_token: \"SOS\", EOS_token: \"EOS\", PAD_token: \"PAD\"}\n", "        self.n_words = 3  # 计算 SOS, EOS, PAD\n", "\n", "    def addSentence(self, sentence):\n", "        for word in sentence.split(' '):\n", "            self.addWord(word)\n", "\n", "    def addWord(self, word):\n", "        if word not in self.word2index:\n", "            self.word2index[word] = self.n_words\n", "            self.word2count[word] = 1\n", "            self.index2word[self.n_words] = word\n", "            self.n_words += 1\n", "        else:\n", "            self.word2count[word] += 1\n", "\n", "# --- 3. 文本预处理函数 ---\n", "def unicodeTo<PERSON>(s):\n", "    return ''.join(\n", "        c for c in unicodedata.normalize('NFD', s)\n", "        if unicodedata.category(c) != 'Mn'\n", "    )\n", "\n", "def normalizeString(s):\n", "    s = unicodeToAscii(s.lower().strip())\n", "    s = re.sub(r\"([.!?,'\\[\\]\\\"()])\", r\" \\1 \", s)\n", "    s = re.sub(r'\\s+', ' ', s).strip()\n", "    return s\n", "\n", "# --- 4. 读取和处理数据 (已修正) ---\n", "def read_data(path):\n", "    df = pd.read_csv(path)\n", "\n", "    # 将列名重命名为小写\n", "    df.rename(columns={\n", "        'Title': 'title',\n", "        'Ingredients': 'ingredients',\n", "        'Recipe': 'recipe'\n", "    }, inplace=True, errors='ignore')\n", "\n", "    # 格式化 ingredients: \"['ing1', 'ing2']\" -> \"ing1 , ing2\"\n", "    df['ingredients'] = df['ingredients'].apply(lambda x: ', '.join(eval(x)))\n", "\n", "    # **新修正部分：**\n", "    # 格式化 recipe: \"['step 1', 'step 2']\" -> \"step 1 step 2\"\n", "    # 这样可以移除列表格式的标点符号，并将所有步骤合并成一个长字符串\n", "    df['recipe'] = df['recipe'].apply(lambda x: ' '.join(eval(x)))\n", "\n", "    pairs = df[['ingredients', 'recipe']].values.tolist()\n", "    return pairs\n", "\n", "def prepareData(lang1_name, lang2_name, path):\n", "    print(\"Reading lines...\")\n", "    pairs = read_data(path)\n", "    print(f\"Read {len(pairs)} sentence pairs\")\n", "\n", "    pairs = [[normalizeString(s) for s in pair] for pair in pairs]\n", "\n", "    input_lang = Lang(lang1_name)\n", "    output_lang = Lang(lang2_name)\n", "\n", "    return input_lang, output_lang, pairs\n", "\n", "# --- 5. 主数据准备流程 ---\n", "input_lang, output_lang, train_pairs = prepareData('ingredients', 'recipe', train_path)\n", "dev_pairs = [[normalizeString(s) for s in pair] for pair in read_data(dev_path)]\n", "test_pairs = [[normalizeString(s) for s in pair] for pair in read_data(test_path)]\n", "\n", "print(\"Building vocabulary...\")\n", "for pair in train_pairs:\n", "    input_lang.addSentence(pair[0])\n", "    output_lang.addSentence(pair[1])\n", "print(\"Vocabulary built!\")\n", "print(f\"Input language ({input_lang.name}) words: {input_lang.n_words}\")\n", "print(f\"Output language ({output_lang.name}) words: {output_lang.n_words}\")\n", "print(\"\\nSample data pair:\")\n", "print(random.choice(train_pairs))\n", "\n", "# --- 6. 将文本转换为 Tensor ---\n", "def indexesFromSentence(lang, sentence):\n", "    # 增加一个检查，如果单词不在词汇表中（可能出现在 dev/test 集），则跳过\n", "    return [lang.word2index[word] for word in sentence.split(' ') if word in lang.word2index]\n", "\n", "def tensorFromSentence(lang, sentence):\n", "    indexes = indexesFromSentence(lang, sentence)\n", "    indexes.append(EOS_token)\n", "    return torch.tensor(indexes, dtype=torch.long, device=device).view(-1, 1)\n", "\n", "def tensorsFromPair(pair):\n", "    input_tensor = tensorFromSentence(input_lang, pair[0])\n", "    target_tensor = tensorFromSentence(output_lang, pair[1])\n", "    return (input_tensor, target_tensor)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pcQETfVSj1aU", "outputId": "db0e44e5-ee2a-4fe5-e51a-864c505f0368"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input padded shape: torch.Size([32, 108])\n", "Input lengths: <PERSON>.<PERSON><PERSON>([32])\n", "Target padded shape: torch.Si<PERSON>([32, 92])\n", "Target lengths: <PERSON><PERSON><PERSON><PERSON>([32])\n"]}], "source": ["class RecipeDataset(Dataset):\n", "    def __init__(self, pairs):\n", "        self.pairs = pairs\n", "\n", "    def __len__(self):\n", "        return len(self.pairs)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.pairs[idx]\n", "\n", "def collate_fn(batch):\n", "    # 按输入序列长度降序排序（对 pack_padded_sequence 很重要）\n", "    batch.sort(key=lambda x: len(x[0].split(' ')), reverse=True)\n", "\n", "    input_sentences, target_sentences = zip(*batch)\n", "\n", "    input_tensors = [tensorFromSentence(input_lang, s).squeeze(1) for s in input_sentences]\n", "    target_tensors = [tensorFromSentence(output_lang, s).squeeze(1) for s in target_sentences]\n", "\n", "    # 计算每个序列的原始长度\n", "    input_lengths = [len(t) for t in input_tensors]\n", "    target_lengths = [len(t) for t in target_tensors]\n", "\n", "    # 填充序列\n", "    input_padded = nn.utils.rnn.pad_sequence(input_tensors, batch_first=True, padding_value=PAD_token)\n", "    target_padded = nn.utils.rnn.pad_sequence(target_tensors, batch_first=True, padding_value=PAD_token)\n", "\n", "    return input_padded, torch.tensor(input_lengths), target_padded, torch.tensor(target_lengths)\n", "\n", "\n", "BATCH_SIZE = 32\n", "\n", "train_dataset = RecipeDataset(train_pairs)\n", "dev_dataset = RecipeDataset(dev_pairs)\n", "test_dataset = RecipeDataset(test_pairs)\n", "\n", "train_dataloader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, collate_fn=collate_fn)\n", "dev_dataloader = DataLoader(dev_dataset, batch_size=BATCH_SIZE, shuffle=False, collate_fn=collate_fn)\n", "test_dataloader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, collate_fn=collate_fn)\n", "\n", "# 打印一个批次的数据形状以验证\n", "for batch in train_dataloader:\n", "    input_padded, input_lengths, target_padded, target_lengths = batch\n", "    print(\"Input padded shape:\", input_padded.shape)\n", "    print(\"Input lengths:\", input_lengths.shape)\n", "    print(\"Target padded shape:\", target_padded.shape)\n", "    print(\"Target lengths:\", target_lengths.shape)\n", "    break"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "ZlOR5usmj1b8"}, "outputs": [], "source": ["class EncoderRNN(nn.Module):\n", "    def __init__(self, input_size, hidden_size, num_layers=1):\n", "        super(Encode<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "\n", "        self.embedding = nn.Embedding(input_size, hidden_size)\n", "        self.gru = nn.GRU(hidden_size, hidden_size, num_layers=num_layers, batch_first=True)\n", "\n", "    def forward(self, input, input_lengths, hidden):\n", "        embedded = self.embedding(input)\n", "        packed = nn.utils.rnn.pack_padded_sequence(embedded, input_lengths.cpu(), batch_first=True)\n", "        outputs, hidden = self.gru(packed, hidden)\n", "        outputs, _ = nn.utils.rnn.pad_packed_sequence(outputs, batch_first=True)\n", "        return outputs, hidden\n", "\n", "    def initHidden(self, batch_size):\n", "        return torch.zeros(self.num_layers, batch_size, self.hidden_size, device=device)\n", "\n", "class DecoderRNN(nn.Module):\n", "    def __init__(self, hidden_size, output_size, num_layers=1):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "\n", "        self.embedding = nn.Embedding(output_size, hidden_size)\n", "        self.gru = nn.GRU(hidden_size, hidden_size, num_layers=num_layers, batch_first=True)\n", "        self.out = nn.Linear(hidden_size, output_size)\n", "        self.softmax = nn.LogSoftmax(dim=1)\n", "\n", "    def forward(self, input, hidden):\n", "        output = self.embedding(input).view(input.size(0), 1, -1)\n", "        output = F.relu(output)\n", "        output, hidden = self.gru(output, hidden)\n", "        output = self.softmax(self.out(output.squeeze(1)))\n", "        return output, hidden\n", "\n", "class Seq2Seq(nn.Module):\n", "    def __init__(self, encoder, decoder, device):\n", "        super(Seq2Seq, self).__init__()\n", "        self.encoder = encoder\n", "        self.decoder = decoder\n", "        self.device = device\n", "\n", "    def forward(self, src, src_len, trg, teacher_forcing_ratio=0.5):\n", "        batch_size = trg.shape[0]\n", "        trg_len = trg.shape[1]\n", "        trg_vocab_size = self.decoder.out.out_features\n", "\n", "        # 存储解码器输出的张量\n", "        outputs = torch.zeros(batch_size, trg_len, trg_vocab_size).to(self.device)\n", "\n", "        # 编码器的最后一个隐藏状态作为解码器的初始隐藏状态\n", "        encoder_hidden = self.encoder.initHidden(batch_size)\n", "        _, encoder_hidden = self.encoder(src, src_len, encoder_hidden)\n", "\n", "        # 第一个输入是 SOS token\n", "        decoder_input = torch.full((batch_size, 1), SOS_token, device=self.device, dtype=torch.long)\n", "\n", "        decoder_hidden = encoder_hidden\n", "\n", "        use_teacher_forcing = True if random.random() < teacher_forcing_ratio else False\n", "\n", "        if use_teacher_forcing:\n", "            # 使用 Teacher forcing：将真实目标作为下一个输入\n", "            for t in range(trg_len -1):\n", "                decoder_output, decoder_hidden = self.decoder(decoder_input.squeeze(1), decoder_hidden)\n", "                outputs[:, t, :] = decoder_output\n", "                decoder_input = trg[:, t+1].unsqueeze(1) # Teacher forcing\n", "        else:\n", "            # 不使用 Teacher forcing：使用自己的预测作为下一个输入\n", "            for t in range(trg_len - 1):\n", "                decoder_output, decoder_hidden = self.decoder(decoder_input.squeeze(1), decoder_hidden)\n", "                outputs[:, t, :] = decoder_output\n", "                topv, topi = decoder_output.topk(1)\n", "                decoder_input = topi.detach() # 从历史中分离\n", "\n", "        return outputs"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 388}, "id": "7R3R9kjXj1eA", "outputId": "b3f99f40-e123-4995-ad5e-afa1b73415ba"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发现最新检查点文件: ./checkpoint_baseline1_epoch3.pth，正在加载...\n", "加载成功！将从 epoch 4 继续训练。\n", "开始训练，从 epoch 4 到 10...\n", "53m 49s (- 322m 57s) (4 40%) 5.3412\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch4.pth\n", "107m 36s (- 269m 0s) (5 50%) 5.2755\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch5.pth\n", "161m 27s (- 215m 16s) (6 60%) 5.2263\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch6.pth\n", "215m 23s (- 161m 32s) (7 70%) 5.1980\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch7.pth\n", "269m 14s (- 107m 41s) (8 80%) 5.1374\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch8.pth\n", "323m 1s (- 53m 50s) (9 90%) 5.0960\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch9.pth\n", "376m 52s (- 0m 0s) (10 100%) 5.0575\n", "已自动保存检查点到: ./checkpoint_baseline1_epoch10.pth\n", "训练完成！\n"]}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# --- 全自动超级版单元格 7：自动寻找、加载、保存 ---\n", "\n", "# 导入新增的库\n", "import glob\n", "import re\n", "\n", "# (辅助函数 asMinutes, timeSince, train, showPlot 保持不变)\n", "def asMinutes(s):\n", "    m = math.floor(s / 60)\n", "    s -= m * 60\n", "    return '%dm %ds' % (m, s)\n", "\n", "def timeSince(since, percent):\n", "    now = time.time()\n", "    s = now - since\n", "    es = s / (percent)\n", "    rs = es - s\n", "    return '%s (- %s)' % (asMinutes(s), asMinutes(rs))\n", "\n", "def train(model, dataloader, optimizer, criterion):\n", "    model.train()\n", "    epoch_loss = 0\n", "    for i, batch in enumerate(dataloader):\n", "        src, src_len, trg, _ = batch\n", "        src, trg = src.to(device), trg.to(device)\n", "        optimizer.zero_grad()\n", "        output = model(src, src_len, trg)\n", "        output_dim = output.shape[-1]\n", "        output = output[:, :-1, :].reshape(-1, output_dim)\n", "        trg = trg[:, 1:].reshape(-1)\n", "        loss = criterion(output, trg)\n", "        loss.backward()\n", "        torch.nn.utils.clip_grad_norm_(model.parameters(), 1)\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "    return epoch_loss / len(dataloader)\n", "\n", "def showPlot(points):\n", "    plt.figure()\n", "    fig, ax = plt.subplots()\n", "    loc = ticker.MultipleLocator(base=0.2)\n", "    ax.yaxis.set_major_locator(loc)\n", "    plt.plot(points)\n", "    plt.show()\n", "\n", "# --- 训练主流程 ---\n", "\n", "# 1. 初始化模型和优化器\n", "HIDDEN_SIZE = 256\n", "NUM_LAYERS = 1\n", "N_EPOCHS = 10\n", "LEARNING_RATE = 0.01\n", "\n", "encoder1 = EncoderRNN(input_lang.n_words, HIDDEN_SIZE, NUM_LAYERS).to(device)\n", "decoder1 = DecoderRNN(HIDDEN_SIZE, output_lang.n_words, NUM_LAYERS).to(device)\n", "model_baseline1 = Seq2Seq(encoder1, decoder1, device).to(device)\n", "\n", "optimizer = optim.SGD(model_baseline1.parameters(), lr=LEARNING_RATE)\n", "criterion = nn.NLLLoss(ignore_index=PAD_token)\n", "\n", "\n", "# --- 新增：自动寻找最新检查点 ---\n", "latest_epoch = -1\n", "CHECKPOINT_PATH = None\n", "checkpoint_files = glob.glob('./checkpoint_baseline1_epoch*.pth')\n", "\n", "if checkpoint_files:\n", "    for f in checkpoint_files:\n", "        # 从文件名中用正则表达式提取 epoch 数字\n", "        match = re.search(r'epoch(\\d+)\\.pth', f)\n", "        if match:\n", "            epoch_num = int(match.group(1))\n", "            if epoch_num > latest_epoch:\n", "                latest_epoch = epoch_num\n", "                CHECKPOINT_PATH = f\n", "# --- 自动寻找结束 ---\n", "\n", "\n", "# 2. 修改后的加载逻辑\n", "start_epoch = 1\n", "plot_losses = []\n", "\n", "# 如果找到了最新的检查点文件，就加载它\n", "if CHECKPOINT_PATH:\n", "    print(f\"发现最新检查点文件: {CHECKPOINT_PATH}，正在加载...\")\n", "    checkpoint = torch.load(CHECKPOINT_PATH, map_location=device)\n", "    model_baseline1.load_state_dict(checkpoint['model_state_dict'])\n", "    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "    start_epoch = checkpoint['epoch'] + 1\n", "    print(f\"加载成功！将从 epoch {start_epoch} 继续训练。\")\n", "else:\n", "    print(\"未发现任何检查点，将从头开始训练。\")\n", "\n", "\n", "# 3. 带自动保存功能的训练循环 (无需改动)\n", "print(f\"开始训练，从 epoch {start_epoch} 到 {N_EPOCHS}...\")\n", "start_time = time.time()\n", "\n", "for epoch in range(start_epoch, N_EPOCHS + 1):\n", "    loss = train(model_baseline1, train_dataloader, optimizer, criterion)\n", "    plot_losses.append(loss)\n", "    \n", "    print('%s (%d %d%%) %.4f' % (timeSince(start_time, (epoch - start_epoch + 1) / (N_EPOCHS - start_epoch + 1)),\n", "                                     epoch, epoch / N_EPOCHS * 100, loss))\n", "    \n", "    SAVE_PATH_AUTO = f\"./checkpoint_baseline1_epoch{epoch}.pth\"\n", "    torch.save({\n", "        'epoch': epoch,\n", "        'model_state_dict': model_baseline1.state_dict(),\n", "        'optimizer_state_dict': optimizer.state_dict(),\n", "        'loss': loss,\n", "    }, SAVE_PATH_AUTO)\n", "    print(f\"已自动保存检查点到: {SAVE_PATH_AUTO}\")\n", "\n", "print(\"训练完成！\")\n", "showPlot(plot_losses)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "t7cX7x1Mj1gA"}, "outputs": [], "source": ["def evaluate(model, dataloader):\n", "    model.eval()\n", "    predictions = []\n", "    actuals = []\n", "\n", "    with torch.no_grad():\n", "        for i, batch in enumerate(dataloader):\n", "            src, src_len, trg, _ = batch\n", "            src, trg = src.to(device), trg.to(device)\n", "\n", "            # 生成预测\n", "            output = model(src, src_len, trg, teacher_forcing_ratio=0) # 在评估时关闭 teacher forcing\n", "\n", "            # output: [batch_size, trg_len, vocab_size]\n", "            # 取概率最高的 token 作为预测结果\n", "            predicted_indices = output.argmax(2) # [batch_size, trg_len]\n", "\n", "            # 将索引转换回单词\n", "            for j in range(predicted_indices.shape[0]):\n", "                pred_list = []\n", "                for token_idx in predicted_indices[j]:\n", "                    token_idx = token_idx.item()\n", "                    if token_idx == EOS_token:\n", "                        break\n", "                    if token_idx != PAD_token and token_idx != SOS_token:\n", "                         pred_list.append(output_lang.index2word[token_idx])\n", "                predictions.append(' '.join(pred_list))\n", "\n", "            # 准备真实食谱\n", "            for j in range(trg.shape[0]):\n", "                trg_list = []\n", "                # 从 1 开始以跳过 SOS token\n", "                for token_idx in trg[j, 1:]:\n", "                    token_idx = token_idx.item()\n", "                    if token_idx == EOS_token:\n", "                        break\n", "                    if token_idx != PAD_token:\n", "                        trg_list.append(output_lang.index2word[token_idx])\n", "                actuals.append(' '.join(trg_list))\n", "\n", "    return predictions, actuals\n", "\n", "def calculate_scores(predictions, actuals):\n", "    bleu4_scores = []\n", "    meteor_scores = []\n", "\n", "    # 将真实文本和预测文本分词，用于 NLTK\n", "    tokenized_actuals_for_nltk = [[sent.split()] for sent in actuals]\n", "    tokenized_preds_for_nltk = [sent.split() for sent in predictions]\n", "\n", "    # 计算 BLEU 和 METEOR\n", "    smoothie = SmoothingFunction().method4\n", "    for i in range(len(tokenized_preds_for_nltk)):\n", "        # BLEU\n", "        bleu_score = sentence_bleu(tokenized_actuals_for_nltk[i], tokenized_preds_for_nltk[i], weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothie)\n", "        bleu4_scores.append(bleu_score)\n", "\n", "        # METEOR (NLTK 的 meteor_score 需要分词后的列表)\n", "        # Note: meteor_score takes a list of references, but we have one.\n", "        m_score = meteor_score(tokenized_actuals_for_nltk[i], tokenized_preds_for_nltk[i])\n", "        meteor_scores.append(m_score)\n", "\n", "    # 计算 BERTScore\n", "    # bert-score 需要字符串列表\n", "    P, R, <PERSON> = bert_scorer(predictions, actuals, lang='en', verbose=False)\n", "\n", "    avg_bleu = np.mean(bleu4_scores)\n", "    avg_meteor = np.mean(meteor_scores)\n", "    avg_bert_f1 = F1.mean().item()\n", "\n", "    print(f\"Average BLEU-4 Score: {avg_bleu:.4f}\")\n", "    print(f\"Average METEOR Score: {avg_meteor:.4f}\")\n", "    print(f\"Average BERTScore (F1): {avg_bert_f1:.4f}\")\n", "\n", "    return avg_bleu, avg_meteor, avg_bert_f1\n", "\n", "# --- 在测试集上运行评估 ---\n", "print(\"\\nEvaluating on Test Set...\")\n", "predictions, actuals = evaluate(model_baseline1, test_dataloader)\n", "calculate_scores(predictions, actuals)\n", "\n", "\n", "# --- 打印一些样本 ---\n", "print(\"\\n--- Sample Predictions ---\")\n", "for i in range(5):\n", "    idx = random.randint(0, len(predictions)-1)\n", "    print(f\"ACTUAL: {actuals[idx]}\")\n", "    print(f\"PREDICTED: {predictions[idx]}\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MhwT5o9gj1iH"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UHjZiJ6fj1kC"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}