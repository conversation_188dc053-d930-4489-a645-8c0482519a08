# Baseline 2: Seq2Seq with Attention Mechanism
# 这个文件包含带注意力机制的模型定义

import torch
import torch.nn as nn
import torch.nn.functional as F
import random

class AttentionEncoderRNN(nn.Module):
    """带注意力机制的编码器 - 返回所有时间步的输出"""
    def __init__(self, input_size, hidden_size, num_layers=1):
        super(AttentionEncoderRNN, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.embedding = nn.Embedding(input_size, hidden_size)
        self.gru = nn.GRU(hidden_size, hidden_size, num_layers=num_layers, batch_first=True)
    
    def forward(self, input, input_lengths, hidden):
        embedded = self.embedding(input)
        packed = nn.utils.rnn.pack_padded_sequence(embedded, input_lengths.cpu(), batch_first=True)
        outputs, hidden = self.gru(packed, hidden)
        outputs, _ = nn.utils.rnn.pad_packed_sequence(outputs, batch_first=True)
        return outputs, hidden
    
    def initHidden(self, batch_size, device):
        return torch.zeros(self.num_layers, batch_size, self.hidden_size, device=device)

class Attention(nn.Module):
    """注意力机制模块"""
    def __init__(self, hidden_size):
        super(Attention, self).__init__()
        self.hidden_size = hidden_size
        self.attn = nn.Linear(hidden_size * 2, hidden_size)
        self.v = nn.Parameter(torch.rand(hidden_size))
        
    def forward(self, hidden, encoder_outputs):
        """
        hidden: [batch_size, hidden_size] - 解码器当前隐藏状态
        encoder_outputs: [batch_size, seq_len, hidden_size] - 编码器所有输出
        """
        batch_size = encoder_outputs.size(0)
        seq_len = encoder_outputs.size(1)
        
        # 重复解码器隐藏状态以匹配编码器输出的序列长度
        hidden = hidden.unsqueeze(1).repeat(1, seq_len, 1)  # [batch_size, seq_len, hidden_size]
        
        # 计算注意力能量
        energy = torch.tanh(self.attn(torch.cat((hidden, encoder_outputs), dim=2)))  # [batch_size, seq_len, hidden_size]
        energy = energy.permute(0, 2, 1)  # [batch_size, hidden_size, seq_len]
        
        # 计算注意力权重
        v = self.v.repeat(batch_size, 1).unsqueeze(1)  # [batch_size, 1, hidden_size]
        attention_weights = torch.bmm(v, energy).squeeze(1)  # [batch_size, seq_len]
        
        return F.softmax(attention_weights, dim=1)

class AttentionDecoderRNN(nn.Module):
    """带注意力机制的解码器"""
    def __init__(self, hidden_size, output_size, num_layers=1):
        super(AttentionDecoderRNN, self).__init__()
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.num_layers = num_layers
        
        self.embedding = nn.Embedding(output_size, hidden_size)
        self.attention = Attention(hidden_size)
        self.gru = nn.GRU(hidden_size * 2, hidden_size, num_layers=num_layers, batch_first=True)
        self.out = nn.Linear(hidden_size, output_size)
        self.softmax = nn.LogSoftmax(dim=1)
        
    def forward(self, input, hidden, encoder_outputs):
        """
        input: [batch_size, 1] - 当前输入token
        hidden: [num_layers, batch_size, hidden_size] - 解码器隐藏状态
        encoder_outputs: [batch_size, seq_len, hidden_size] - 编码器所有输出
        """
        embedded = self.embedding(input)  # [batch_size, 1, hidden_size]
        
        # 计算注意力权重
        attention_weights = self.attention(hidden[-1], encoder_outputs)  # [batch_size, seq_len]
        
        # 计算上下文向量
        context = torch.bmm(attention_weights.unsqueeze(1), encoder_outputs)  # [batch_size, 1, hidden_size]
        
        # 将嵌入和上下文向量连接
        rnn_input = torch.cat((embedded, context), dim=2)  # [batch_size, 1, hidden_size * 2]
        
        # 通过GRU
        output, hidden = self.gru(rnn_input, hidden)
        
        # 生成输出概率
        output = self.softmax(self.out(output.squeeze(1)))  # [batch_size, output_size]
        
        return output, hidden, attention_weights

class Seq2SeqWithAttention(nn.Module):
    """带注意力机制的Seq2Seq模型"""
    def __init__(self, encoder, decoder, device):
        super(Seq2SeqWithAttention, self).__init__()
        self.encoder = encoder
        self.decoder = decoder
        self.device = device
    
    def forward(self, src, src_len, trg, teacher_forcing_ratio=0.5):
        batch_size = trg.shape[0]
        trg_len = trg.shape[1]
        trg_vocab_size = self.decoder.output_size
        
        # 存储解码器输出
        outputs = torch.zeros(batch_size, trg_len, trg_vocab_size).to(self.device)
        
        # 编码
        encoder_hidden = self.encoder.initHidden(batch_size, self.device)
        encoder_outputs, encoder_hidden = self.encoder(src, src_len, encoder_hidden)
        
        # 解码器初始化
        decoder_input = torch.full((batch_size, 1), 1, device=self.device, dtype=torch.long)  # SOS_token = 1
        decoder_hidden = encoder_hidden
        
        use_teacher_forcing = True if random.random() < teacher_forcing_ratio else False
        
        if use_teacher_forcing:
            # Teacher forcing: 使用真实目标作为下一个输入
            for t in range(1, trg_len):
                output, decoder_hidden, attention_weights = self.decoder(
                    decoder_input, decoder_hidden, encoder_outputs)
                outputs[:, t] = output
                decoder_input = trg[:, t].unsqueeze(1)  # 使用真实目标
        else:
            # 不使用teacher forcing: 使用模型自己的预测
            for t in range(1, trg_len):
                output, decoder_hidden, attention_weights = self.decoder(
                    decoder_input, decoder_hidden, encoder_outputs)
                outputs[:, t] = output
                decoder_input = output.argmax(1).unsqueeze(1)  # 使用预测结果
        
        return outputs
