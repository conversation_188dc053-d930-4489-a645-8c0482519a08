# === 第10个Cell: Baseline 2 训练 ===

# 1. Baseline 2 模型参数设置
HIDDEN_SIZE_B2 = 256
NUM_LAYERS_B2 = 1
N_EPOCHS_B2 = 15  # 比Baseline 1多训练一些epoch
LEARNING_RATE_B2 = 0.001  # 降低学习率

# 2. 创建Baseline 2模型实例
encoder2 = AttentionEncoderRNN(input_lang.n_words, HIDDEN_SIZE_B2, NUM_LAYERS_B2).to(device)
decoder2 = AttentionDecoderRNN(HIDDEN_SIZE_B2, output_lang.n_words, NUM_LAYERS_B2).to(device)
model_baseline2 = Seq2SeqWithAttention(encoder2, decoder2, device).to(device)

# 3. 优化器和损失函数
optimizer_b2 = optim.Adam(model_baseline2.parameters(), lr=LEARNING_RATE_B2)  # 使用Adam优化器
criterion_b2 = nn.NLLLoss(ignore_index=PAD_token)

print(f"Baseline 2 模型参数数量: {sum(p.numel() for p in model_baseline2.parameters() if p.requires_grad)}")

# 4. 检查点加载逻辑
CHECKPOINT_PATH_B2 = './checkpoint_baseline2_epoch15.pth'
plot_losses_b2 = []
start_epoch_b2 = 1

# 检查是否存在检查点文件
checkpoint_files_b2 = [f for f in os.listdir('.') if f.startswith('checkpoint_baseline2_epoch') and f.endswith('.pth')]
if checkpoint_files_b2:
    # 找到最新的检查点
    latest_checkpoint_b2 = max(checkpoint_files_b2, key=lambda x: int(x.split('epoch')[1].split('.')[0]))
    checkpoint_path_b2 = f'./{latest_checkpoint_b2}'
    print(f"发现Baseline 2检查点文件: {checkpoint_path_b2}，正在加载...")
    
    checkpoint_b2 = torch.load(checkpoint_path_b2, map_location=device)
    model_baseline2.load_state_dict(checkpoint_b2['model_state_dict'])
    optimizer_b2.load_state_dict(checkpoint_b2['optimizer_state_dict'])
    start_epoch_b2 = checkpoint_b2['epoch'] + 1
    
    print(f"Baseline 2加载成功！将从 epoch {start_epoch_b2} 继续训练。")
else:
    print("未找到Baseline 2检查点文件，从头开始训练。")

# 5. 训练循环（带检查）
if start_epoch_b2 <= N_EPOCHS_B2:
    print(f"开始训练Baseline 2，从 epoch {start_epoch_b2} 到 {N_EPOCHS_B2}...")
    start_time = time.time()
    
    for epoch in range(start_epoch_b2, N_EPOCHS_B2 + 1):
        loss = train(model_baseline2, train_dataloader, optimizer_b2, criterion_b2)
        plot_losses_b2.append(loss)
        
        print('%s (%d %d%%) %.4f' % (timeSince(start_time, (epoch - start_epoch_b2 + 1) / (N_EPOCHS_B2 - start_epoch_b2 + 1)),
                                         epoch, epoch / N_EPOCHS_B2 * 100, loss))
        
        SAVE_PATH_AUTO_B2 = f"./checkpoint_baseline2_epoch{epoch}.pth"
        torch.save({
            'epoch': epoch,
            'model_state_dict': model_baseline2.state_dict(),
            'optimizer_state_dict': optimizer_b2.state_dict(),
            'loss': loss,
        }, SAVE_PATH_AUTO_B2)
else:
    print(f"Baseline 2已经训练了 {start_epoch_b2-1} 个epoch，达到目标 {N_EPOCHS_B2} epoch，跳过训练。")

print("Baseline 2训练完成！")

# 6. 绘制损失曲线（如果有新的训练）
if plot_losses_b2:
    plt.figure(figsize=(10, 6))
    plt.plot(plot_losses_b2, label='Baseline 2 (with Attention)')
    plt.title('Baseline 2 Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    plt.show()
else:
    print("没有新的Baseline 2训练数据，跳过损失曲线绘制。")
